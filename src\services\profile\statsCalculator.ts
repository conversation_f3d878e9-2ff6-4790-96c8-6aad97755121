/**
 * 🧮 STATS CALCULATOR SERVICE
 * Gestisce il calcolo delle statistiche dopo le partite
 */

import { calculateGameXp, calculateLevelFromXp } from "../experienceSystem";
import type { PlayerStats, GameResult } from "../localStatsService";

export interface GameData {
  isWinner: boolean;
  difficulty: "easy" | "medium" | "hard";
  playerTeam: number;
  finalScore: [number, number];
  maraffeMade?: number;
  isPerfectGame?: boolean;
  isComeback?: boolean;
  isDominantWin?: boolean;
  isAbandoned?: boolean;
}

export interface StatsUpdateResult {
  stats: PlayerStats;
  leveledUp: boolean;
  xpGained: number;
  xpBreakdown: string[];
}

/**
 * Calcola l'aggiornamento delle statistiche senza modificare i dati originali
 */
export const calculateStatsUpdate = (
  currentStats: PlayerStats,
  gameData: GameData
): StatsUpdateResult => {
  const stats = { ...currentStats }; // Copia delle statistiche correnti
  const oldLevel = stats.level;
  const now = new Date().toISOString();

  // Calcola XP
  let xpResult;
  if (gameData.isAbandoned) {
    xpResult = {
      totalXp: 0,
      breakdown: ["Partita abbandonata: 0 XP"],
    };
  } else {
    xpResult = calculateGameXp({
      isWinner: gameData.isWinner,
      difficulty: gameData.difficulty,
      maraffeMade: gameData.maraffeMade,
      isPerfectGame: gameData.isPerfectGame,
      isComeback: gameData.isComeback,
      isDominantWin: gameData.isDominantWin,
      isFirstWinOfDay: false, // Per ora non tracciamo questo online
      currentWinStreak: stats.currentWinStreak,
    });
  }

  // Aggiorna statistiche
  stats.xp += xpResult.totalXp;
  stats.totalGames += 1;
  stats.lastPlayed = now;

  // Aggiorna vittorie/sconfitte
  const isActualWinner = gameData.isWinner && !gameData.isAbandoned;
  if (isActualWinner) {
    stats.gamesWon += 1;
    stats.currentWinStreak += 1;
    stats.bestWinStreak = Math.max(stats.bestWinStreak, stats.currentWinStreak);
  } else {
    stats.gamesLost += 1;
    stats.currentWinStreak = 0;
  }

  // Calcola win rate
  stats.winRate =
    stats.totalGames > 0
      ? Math.round((stats.gamesWon / stats.totalGames) * 100)
      : 0;

  // Aggiorna maraffe
  if (gameData.maraffeMade) {
    stats.maraffeMade += gameData.maraffeMade;
  }

  // Calcola nuovo livello
  stats.level = calculateLevelFromXp(stats.xp);
  const leveledUp = stats.level > oldLevel;

  // Aggiorna recent games (solo per la cache locale)
  const gameResult: GameResult = {
    id: `game_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    date: now,
    result: gameData.isAbandoned
      ? "Sconfitta"
      : gameData.isWinner
      ? "Vittoria"
      : "Sconfitta",
    score: gameData.isAbandoned
      ? "Abbandonata"
      : `${gameData.finalScore[gameData.playerTeam]}-${
          gameData.finalScore[gameData.playerTeam === 0 ? 1 : 0]
        }`,
    difficulty: gameData.difficulty,
    playerTeam: gameData.playerTeam,
    xpGained: xpResult.totalXp,
    xpBreakdown: xpResult.breakdown,
  };

  // Aggiorna recent games (inizializza se non esiste)
  if (!stats.recentGames) {
    stats.recentGames = [];
  }

  if (stats.recentGames.length >= 5) {
    stats.recentGames.pop();
  }
  stats.recentGames.unshift(gameResult);

  stats.updatedAt = now;

  return {
    stats,
    leveledUp,
    xpGained: xpResult.totalXp,
    xpBreakdown: xpResult.breakdown,
  };
};

/**
 * Calcola l'aggiornamento delle statistiche per abbandono
 */
export const calculateAbandonmentUpdate = (
  currentStats: PlayerStats,
  abandonmentData: {
    difficulty: "easy" | "medium" | "hard";
    playerTeam: number;
    currentScore: [number, number];
  }
): StatsUpdateResult => {
  return calculateStatsUpdate(currentStats, {
    isWinner: false,
    difficulty: abandonmentData.difficulty,
    playerTeam: abandonmentData.playerTeam,
    finalScore: abandonmentData.currentScore,
    isAbandoned: true,
  });
};
