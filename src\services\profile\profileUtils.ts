/**
 * 🛠️ PROFILE UTILITIES
 * Funzioni di utilità per la gestione dei profili
 */

import type { PlayerStats } from "../localStatsService";

/**
 * Gestisce date retrocompatibili per profili esistenti
 */
export const getReliableCreatedAt = (
  dbCreatedAt: string | undefined | null,
  gamesPlayed: number,
  fallbackDate: string
): string | undefined => {
  // Se non c'è data dal DB, restituisce undefined per mostrare "Non disponibile"
  if (!dbCreatedAt) {
    return undefined;
  }

  try {
    const createdDate = new Date(dbCreatedAt);
    const now = new Date();
    const daysDiff = Math.floor(
      (now.getTime() - createdDate.getTime()) / (1000 * 60 * 60 * 24)
    );

    // Se la data è molto recente (meno di 30 giorni) ma l'utente ha molte partite,
    // probabilmente è un vecchio utente e la data non è affidabile
    if (daysDiff < 30 && gamesPlayed > 10) {
      console.log(
        `⚠️ Data sospetta per utente con ${gamesPlayed} partite (registrato ${daysDiff} giorni fa)`
      );
      return undefined; // Mostra "Non disponibile" invece di una data inaffidabile
    }

    return dbCreatedAt;
  } catch (error) {
    console.warn("Errore parsing data creazione:", error);
    return undefined;
  }
};

/**
 * Converte le statistiche dal formato database al formato locale
 */
export const convertDbStatsToLocal = (
  dbStats: any,
  username: string
): PlayerStats => {
  const now = new Date().toISOString();

  return {
    level: dbStats.level || 1,
    xp: dbStats.xp || 0,
    totalGames: dbStats.games_played || 0,
    gamesWon: dbStats.games_won || 0,
    gamesLost: Math.max(0, (dbStats.games_played || 0) - (dbStats.games_won || 0)),
    winRate: dbStats.games_played > 0 
      ? Math.round((dbStats.games_won / dbStats.games_played) * 100) 
      : 0,
    currentWinStreak: dbStats.current_win_streak || 0,
    bestWinStreak: dbStats.best_win_streak || 0,
    maraffeMade: dbStats.maraffe_made || 0,
    achievementsUnlocked: [],
    recentGames: [],
    lastPlayed: dbStats.last_played || now,
    createdAt: dbStats.created_at || now,
    updatedAt: dbStats.updated_at || now,
  };
};

/**
 * Valida le statistiche per assicurarsi che siano coerenti
 */
export const validateStats = (stats: PlayerStats): PlayerStats => {
  const validated = { ...stats };

  // Assicurati che i valori numerici siano validi
  validated.level = Math.max(1, validated.level || 1);
  validated.xp = Math.max(0, validated.xp || 0);
  validated.totalGames = Math.max(0, validated.totalGames || 0);
  validated.gamesWon = Math.max(0, validated.gamesWon || 0);
  validated.gamesLost = Math.max(0, validated.gamesLost || 0);
  validated.currentWinStreak = Math.max(0, validated.currentWinStreak || 0);
  validated.bestWinStreak = Math.max(0, validated.bestWinStreak || 0);
  validated.maraffeMade = Math.max(0, validated.maraffeMade || 0);

  // Assicurati che gamesWon + gamesLost = totalGames
  if (validated.gamesWon + validated.gamesLost !== validated.totalGames) {
    validated.gamesLost = Math.max(0, validated.totalGames - validated.gamesWon);
  }

  // Ricalcola win rate
  validated.winRate = validated.totalGames > 0 
    ? Math.round((validated.gamesWon / validated.totalGames) * 100) 
    : 0;

  // Assicurati che bestWinStreak >= currentWinStreak
  validated.bestWinStreak = Math.max(validated.bestWinStreak, validated.currentWinStreak);

  // Inizializza array se non esistono
  if (!validated.achievementsUnlocked) {
    validated.achievementsUnlocked = [];
  }
  if (!validated.recentGames) {
    validated.recentGames = [];
  }

  return validated;
};

/**
 * Crea un profilo locale di fallback
 */
export const createLocalFallbackProfile = () => {
  const { getPlayerStats: getLocalPlayerStats } = require("../localStatsService");
  const stats = getLocalPlayerStats();

  // Ottieni il titolo dinamico basato sul livello attuale
  const { getPlayerTitle } = require("../playerTitlesService");
  const playerTitle = getPlayerTitle(stats.level);

  return {
    stats: validateStats(stats),
    isOnline: false,
    customName: playerTitle.title,
  };
};

/**
 * Funzioni di debug per development
 */
export const createDebugFunctions = () => {
  if (process.env.NODE_ENV !== "development") {
    return {};
  }

  return {
    testVictory: async () => {
      const { updateStatsAfterGame } = await import("../profileService");
      const result = await updateStatsAfterGame({
        isWinner: true,
        difficulty: "medium",
        playerTeam: 0,
        finalScore: [11, 7],
        maraffeMade: 2,
        isPerfectGame: false,
        isComeback: false,
        isDominantWin: false,
        isAbandoned: false,
      });
      console.log("🎯 Test Vittoria:", result);
      return result;
    },
    testDefeat: async () => {
      const { updateStatsAfterGame } = await import("../profileService");
      const result = await updateStatsAfterGame({
        isWinner: false,
        difficulty: "medium",
        playerTeam: 0,
        finalScore: [7, 11],
        maraffeMade: 0,
        isPerfectGame: false,
        isComeback: false,
        isDominantWin: false,
        isAbandoned: false,
      });
      console.log("🎯 Test Sconfitta:", result);
      return result;
    },
    testAbandon: async () => {
      const { updateStatsAfterAbandonment } = await import("../profileService");
      const result = await updateStatsAfterAbandonment({
        difficulty: "medium",
        playerTeam: 0,
        currentScore: [5, 3],
      });
      console.log("🎯 Test Abbandono:", result);
      return result;
    },
  };
};
