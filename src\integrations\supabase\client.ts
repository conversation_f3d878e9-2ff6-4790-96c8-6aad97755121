// This file is automatically generated. Do not edit it directly.
import { createClient } from "@supabase/supabase-js";
import type { Database } from "./types";
import { env } from "../../config/environment";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(
  env.supabase.url,
  env.supabase.anonKey,
  {
    auth: {
      autoRefreshToken: true, // Manteniamo attivo per sicurezza
      persistSession: true,
      storage: localStorage,
      storageKey: "maraffa-romagnola-auth",
      // Configurazione per sessioni più lunghe e più robuste
      detectSessionInUrl: true,
      flowType: "pkce",
      // Configurazioni aggiuntive per persistenza migliorata
      debug: false, // Disabilita debug in produzione
      // 🔥 CONFIGURAZIONE MIGLIORATA PER SESSIONI PERSISTENTI
      refreshTokenRetryAttempts: 10, // Aumentato a 10 tentativi per maggiore resilienza
      refreshTokenRetryDelay: 1000, // Ridotto a 1 secondo per refresh più rapidi
      // Configurazioni aggiuntive per sessioni quasi permanenti
      refreshTokenRetryBackoff: true, // Abilita backoff esponenziale
      refreshTokenRetryJitter: true, // Abilita jitter per evitare thundering herd
    },
    global: {
      headers: {
        "X-Client-Info": "maraffa-romagnola@1.0.0",
      },
      // Aggiungiamo timeout globale migliorato per evitare richieste bloccate
      fetch: (url, options = {}) => {
        const controller = new AbortController();

        // Timeout dinamico basato sul tipo di richiesta
        let timeout = env.security.apiTimeout;
        if (url.includes("/auth/")) {
          timeout = env.security.sessionRefreshTimeout; // Più tempo per auth
        } else if (url.includes("/rest/v1/")) {
          timeout = env.security.apiTimeout; // Tempo standard per API
        }

        const timeoutId = setTimeout(() => {
          console.warn(`⏰ Timeout richiesta dopo ${timeout}ms: ${url}`);
          controller.abort();
        }, timeout);

        return fetch(url, {
          ...options,
          signal: options.signal || controller.signal,
        }).finally(() => {
          clearTimeout(timeoutId);
        });
      },
    },
    // Retry automatico per errori di rete
    db: {
      schema: "public",
    },
    realtime: {
      params: {
        eventsPerSecond: 2,
      },
    },
  }
);

// Sistema di gestione sessioni migliorato con gestione race conditions e persistenza robusta
class SessionManager {
  private static instance: SessionManager;
  private refreshPromise: Promise<any> | null = null;
  private sessionPromise: Promise<any> | null = null;
  private lastRefreshTime = 0;
  private readonly MIN_REFRESH_INTERVAL = 1000; // Ridotto a 1 secondo per maggiore reattività
  private readonly MAX_REFRESH_INTERVAL = 15000; // Ridotto a 15 secondi per refresh più frequenti
  private refreshAttempts = 0;
  private connectionHealthy = true;
  private lastHealthCheck = 0;
  private readonly HEALTH_CHECK_INTERVAL = 30000; // Ridotto a 30 secondi per controlli più frequenti
  private backgroundRefreshTimer: NodeJS.Timeout | null = null;
  private readonly PERSISTENT_SESSION_REFRESH_INTERVAL = 10 * 60 * 1000; // 10 minuti per refresh preventivo
  private persistentRefreshTimer: NodeJS.Timeout | null = null;

  static getInstance(): SessionManager {
    if (!SessionManager.instance) {
      SessionManager.instance = new SessionManager();
    }
    return SessionManager.instance;
  }

  // Verifica se il token è vicino alla scadenza (entro 30 minuti per essere molto più proattivi)
  private isTokenNearExpiry(session: any): boolean {
    if (!session?.expires_at) return false;

    const expiresAt = session.expires_at * 1000;
    const now = Date.now();
    const thirtyMinutes = 30 * 60 * 1000; // Aumentato a 30 minuti per essere molto più proattivi

    return expiresAt - now < thirtyMinutes;
  }

  // Verifica se il token è molto vicino alla scadenza (entro 5 minuti - urgente)
  private isTokenCriticallyNearExpiry(session: any): boolean {
    if (!session?.expires_at) return false;

    const expiresAt = session.expires_at * 1000;
    const now = Date.now();
    const fiveMinutes = 5 * 60 * 1000;

    return expiresAt - now < fiveMinutes;
  }

  // Controlla la salute della connessione
  private async checkConnectionHealth(): Promise<boolean> {
    const now = Date.now();
    if (now - this.lastHealthCheck < this.HEALTH_CHECK_INTERVAL) {
      return this.connectionHealthy;
    }

    try {
      // Test di connettività usando endpoint pubblico (non richiede auth)
      const response = await fetch("https://www.google.com/favicon.ico", {
        method: "HEAD",
        mode: "no-cors",
        signal: AbortSignal.timeout(env.security.connectionHealthTimeout),
      });

      // Con no-cors, response.ok potrebbe essere false anche se la richiesta è riuscita
      // Controlliamo se la richiesta è stata completata (non abortita)
      this.connectionHealthy = response.type === "opaque" || response.ok;
      this.lastHealthCheck = now;

      if (this.connectionHealthy) {
        this.refreshAttempts = 0; // Reset tentativi se connessione OK
        console.log("✅ Controllo salute connessione: OK");
      } else {
        console.warn("⚠️ Controllo salute connessione: FALLITO");
      }

      return this.connectionHealthy;
    } catch (error) {
      console.warn("⚠️ Controllo salute connessione fallito:", error);
      this.connectionHealthy = false;
      this.lastHealthCheck = now;
      return false;
    }
  }

  // Verifica se il token è scaduto
  private isTokenExpired(session: any): boolean {
    if (!session?.expires_at) return false;

    const expiresAt = session.expires_at * 1000;
    const now = Date.now();

    return expiresAt <= now;
  }

  // Avvia refresh automatico in background
  startBackgroundRefresh(): void {
    if (this.backgroundRefreshTimer) {
      clearInterval(this.backgroundRefreshTimer);
    }

    // Controlla ogni 2 minuti se serve un refresh proattivo
    this.backgroundRefreshTimer = setInterval(async () => {
      try {
        const {
          data: { session },
        } = await supabase.auth.getSession();
        if (session && this.isTokenNearExpiry(session)) {
          console.log("🔄 Background refresh proattivo avviato");
          await this.refreshSession();
        }
      } catch (error) {
        console.warn("⚠️ Errore background refresh:", error);
      }
    }, 2 * 60 * 1000); // Ogni 2 minuti
  }

  // Ferma refresh automatico in background
  stopBackgroundRefresh(): void {
    if (this.backgroundRefreshTimer) {
      clearInterval(this.backgroundRefreshTimer);
      this.backgroundRefreshTimer = null;
    }
  }

  // 🔥 NUOVO: Avvia refresh persistente per sessioni quasi permanenti
  startPersistentSessionRefresh(): void {
    if (this.persistentRefreshTimer) {
      clearInterval(this.persistentRefreshTimer);
    }

    // Refresh ogni 10 minuti per mantenere sessioni attive a lungo termine
    this.persistentRefreshTimer = setInterval(async () => {
      try {
        console.log("🔄 Persistent session refresh...");
        const session = await this.ensureValidSession();
        if (session) {
          // Emetti evento per notificare il refresh
          window.dispatchEvent(
            new CustomEvent("sessionRefreshed", {
              detail: { session },
            })
          );
        }
      } catch (error) {
        console.warn("⚠️ Persistent session refresh fallito:", error);
        // In caso di errore, prova un refresh più aggressivo
        try {
          await this.forceRefreshSession();
        } catch (forceError) {
          console.error("❌ Force refresh fallito:", forceError);
        }
      }
    }, this.PERSISTENT_SESSION_REFRESH_INTERVAL);

    console.log("✅ Persistent session refresh avviato (ogni 10 minuti)");
  }

  // Ferma refresh persistente
  stopPersistentSessionRefresh(): void {
    if (this.persistentRefreshTimer) {
      clearInterval(this.persistentRefreshTimer);
      this.persistentRefreshTimer = null;
    }
  }

  // 🔥 NUOVO: Force refresh per situazioni critiche
  async forceRefreshSession(): Promise<any> {
    console.log("🚨 Force refresh sessione...");

    try {
      // Bypassa tutti i controlli e forza un refresh immediato
      this.refreshPromise = null;
      this.sessionPromise = null;
      this.lastRefreshTime = 0;

      const { data, error } = await supabase.auth.refreshSession();

      if (error) {
        throw error;
      }

      if (data?.session) {
        console.log("✅ Force refresh completato con successo");
        return data.session;
      }

      throw new Error("Nessuna sessione ricevuta dal force refresh");
    } catch (error) {
      console.error("❌ Force refresh fallito:", error);
      throw error;
    }
  }

  // Refresh proattivo della sessione con gestione race conditions migliorata
  async ensureValidSession(): Promise<any> {
    // Se c'è già una richiesta in corso, aspetta quella
    if (this.sessionPromise) {
      console.log("⏳ Sessione già in controllo, attendo...");
      return await this.sessionPromise;
    }

    // Crea una nuova promise per questa richiesta con timeout ottimizzato
    this.sessionPromise = Promise.race([
      this._ensureValidSessionInternal(),
      new Promise(
        (_, reject) =>
          setTimeout(
            () => reject(new Error("Timeout ensureValidSession")),
            15000
          ) // Ridotto a 15 secondi
      ),
    ]);

    try {
      const result = await this.sessionPromise;
      this.sessionPromise = null;
      return result;
    } catch (error) {
      this.sessionPromise = null;
      console.error("❌ ensureValidSession fallito:", error);
      throw error;
    }
  }

  private async _ensureValidSessionInternal(): Promise<any> {
    try {
      console.log("🔍 SessionManager: controllo sessione...");

      // Prima controlla la salute della connessione
      const isHealthy = await this.checkConnectionHealth();
      if (!isHealthy) {
        console.warn("⚠️ Connessione non salutare, tentativo recovery...");
      } else {
        console.log("✅ Connessione salutare");
      }

      const {
        data: { session },
        error,
      } = await supabase.auth.getSession();

      if (error) {
        console.error("❌ Errore recupero sessione:", error);
        // Tenta recovery dalla storage locale
        return await this.attemptSessionRecovery();
      }

      if (!session) {
        console.warn("⚠️ Nessuna sessione attiva");
        // Tenta recovery dalla storage locale
        return await this.attemptSessionRecovery();
      }

      // Debug: mostra info sulla sessione
      const expiresAt = session.expires_at ? session.expires_at * 1000 : 0;
      const now = Date.now();
      const timeUntilExpiry = expiresAt - now;
      const minutesUntilExpiry = Math.round(timeUntilExpiry / 60000);

      console.log(
        `🔍 SessionManager: token scade tra ${minutesUntilExpiry} minuti`
      );

      // Se il token è scaduto, forza refresh immediato
      if (this.isTokenExpired(session)) {
        console.log("⚠️ Token scaduto, refresh immediato...");
        return await this.refreshSession();
      }

      // Se il token è criticamente vicino alla scadenza, refresh urgente
      if (this.isTokenCriticallyNearExpiry(session)) {
        console.log(
          "🚨 Token criticamente vicino alla scadenza, refresh urgente..."
        );
        return await this.refreshSession();
      }

      // Se il token è vicino alla scadenza, refresha proattivamente
      if (this.isTokenNearExpiry(session)) {
        console.log("🔄 Token vicino alla scadenza, refresh proattivo...");
        // Non aspettare il refresh, fallo in background
        this.refreshSession().catch((error) =>
          console.warn("⚠️ Refresh proattivo fallito:", error)
        );
      }

      console.log("✅ SessionManager: sessione valida");
      return session;
    } catch (error) {
      console.error("❌ Errore _ensureValidSessionInternal:", error);
      return await this.attemptSessionRecovery();
    }
  }

  // Tentativo di recovery della sessione dalla storage locale
  private async attemptSessionRecovery(): Promise<any> {
    try {
      console.log("🔄 Tentativo recovery sessione dalla storage locale...");

      // Forza il reload della sessione dalla storage
      const { data, error } = await supabase.auth.refreshSession();

      if (error || !data?.session) {
        console.warn("⚠️ Recovery sessione fallito");
        return null;
      }

      console.log("✅ Recovery sessione riuscito");
      return data.session;
    } catch (error) {
      console.error("❌ Errore recovery sessione:", error);
      return null;
    }
  }

  // Refresh della sessione con debouncing intelligente e retry automatico
  async refreshSession(): Promise<any> {
    const now = Date.now();

    // Calcola intervallo dinamico basato sui tentativi falliti
    const dynamicInterval = Math.min(
      this.MIN_REFRESH_INTERVAL * Math.pow(2, this.refreshAttempts),
      this.MAX_REFRESH_INTERVAL
    );

    // Evita refresh troppo frequenti con backoff exponential
    if (now - this.lastRefreshTime < dynamicInterval) {
      console.log(
        `⏳ Refresh troppo recente, skip (attesa ${dynamicInterval}ms)`
      );
      return await supabase.auth.getSession().then((r) => r.data.session);
    }

    // Se c'è già un refresh in corso, aspetta quello
    if (this.refreshPromise) {
      console.log("⏳ Refresh già in corso, attendo...");
      return await this.refreshPromise;
    }

    // Aggiungi timeout al refresh più lungo e con retry
    this.refreshPromise = this._performRefreshWithRetry();

    try {
      const result = await this.refreshPromise;
      this.refreshPromise = null;
      this.refreshAttempts = 0; // Reset tentativi su successo
      return result;
    } catch (error) {
      this.refreshPromise = null;
      this.refreshAttempts++;
      console.error(
        `❌ Refresh fallito (tentativo ${this.refreshAttempts}):`,
        error
      );
      throw error;
    }
  }

  // Esegue refresh con retry automatico
  private async _performRefreshWithRetry(): Promise<any> {
    const maxRetries = 2; // Ridotto a 2 retry per evitare sovraccarico
    let lastError: any;

    for (let attempt = 0; attempt < maxRetries; attempt++) {
      try {
        const timeout = env.security.sessionRefreshTimeout + attempt * 5000; // Base + incremento per tentativo

        const result = await Promise.race([
          this._performRefresh(),
          new Promise((_, reject) =>
            setTimeout(
              () => reject(new Error(`Timeout refreshSession (${timeout}ms)`)),
              timeout
            )
          ),
        ]);

        return result;
      } catch (error) {
        lastError = error;
        console.warn(
          `⚠️ Tentativo refresh ${attempt + 1}/${maxRetries} fallito:`,
          error
        );

        if (attempt < maxRetries - 1) {
          // Attesa progressiva tra tentativi
          const delay = 1000 * (attempt + 1);
          await new Promise((resolve) => setTimeout(resolve, delay));
        }
      }
    }

    throw lastError;
  }

  private async _performRefresh(): Promise<any> {
    try {
      console.log("🔄 Esecuzione refresh sessione...");
      this.lastRefreshTime = Date.now();

      const { data, error } = await supabase.auth.refreshSession();

      if (error) {
        console.error("❌ Errore refresh:", error);

        // Fallback: prova a ottenere la sessione corrente
        const { data: sessionData, error: sessionError } =
          await supabase.auth.getSession();
        if (sessionError || !sessionData?.session) {
          console.error("❌ Fallback sessione fallito:", sessionError);
          throw error;
        }

        console.log("✅ Fallback sessione riuscito");
        return sessionData.session;
      }

      if (data?.session) {
        console.log("✅ Refresh sessione completato");

        // 🔄 NOTIFICA REFRESH SESSIONE PER AGGIORNAMENTO PROFILO
        window.dispatchEvent(
          new CustomEvent("sessionRefreshed", {
            detail: { session: data.session },
          })
        );

        return data.session;
      }

      throw new Error("Nessuna sessione nel refresh");
    } catch (error) {
      console.error("❌ Errore _performRefresh:", error);
      throw error;
    }
  }
}

// Funzione helper migliorata per retry automatico con backoff exponential
export const retryWithRefresh = async <T>(
  operation: () => Promise<T>,
  maxRetries: number = 3
): Promise<T> => {
  const sessionManager = SessionManager.getInstance();

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      // Prima di ogni tentativo, assicurati che la sessione sia valida
      if (attempt > 0) {
        try {
          await sessionManager.ensureValidSession();
        } catch (sessionError) {
          console.warn(
            `⚠️ SessionManager fallito (tentativo ${attempt}), uso fallback diretto`
          );
          // Fallback: usa direttamente supabase.auth.getSession()
          const {
            data: { session },
          } = await supabase.auth.getSession();
          if (!session?.access_token) {
            throw new Error("Nessuna sessione valida disponibile");
          }
        }
      }

      return await operation();
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);

      // Controlla se è un errore di autorizzazione o timeout
      const isAuthError =
        errorMessage.includes("401") ||
        errorMessage.includes("Unauthorized") ||
        errorMessage.includes("JWT expired") ||
        errorMessage.includes("refresh_token_not_found") ||
        errorMessage.includes("invalid_token") ||
        errorMessage.includes("Invalid JWT") ||
        errorMessage.includes("Timeout ensureValidSession");

      if (!isAuthError || attempt >= maxRetries) {
        console.error(
          `❌ Errore finale (tentativo ${attempt + 1}/${maxRetries + 1}):`,
          errorMessage
        );
        throw error;
      }

      console.log(
        `🔄 Errore auth (tentativo ${attempt + 1}/${
          maxRetries + 1
        }), refresh e retry...`
      );

      try {
        // Prova prima con SessionManager, poi fallback diretto
        try {
          await sessionManager.refreshSession();
        } catch (refreshError) {
          console.warn(
            "⚠️ SessionManager refresh fallito, uso fallback diretto"
          );
          await supabase.auth.refreshSession();
        }

        // Backoff exponential: 1s, 2s, 3s
        const delay = Math.min(1000 * Math.pow(2, attempt), 3000);
        await new Promise((resolve) => setTimeout(resolve, delay));
      } catch (refreshError) {
        console.error("❌ Errore refresh durante retry:", refreshError);
        if (attempt >= maxRetries) {
          throw error;
        }
      }
    }
  }

  throw new Error("Retry esauriti");
};

// Esporta il SessionManager per uso esterno
export const sessionManager = SessionManager.getInstance();

// 🔥 INIZIALIZZAZIONE AUTOMATICA SESSIONI PERSISTENTI
// Avvia automaticamente il refresh persistente quando il modulo viene caricato
if (typeof window !== "undefined") {
  // Avvia il refresh persistente dopo un breve delay per permettere l'inizializzazione
  setTimeout(() => {
    sessionManager.startPersistentSessionRefresh();
    sessionManager.startBackgroundRefresh();
    console.log("🚀 Sistema di sessioni persistenti inizializzato");
  }, 2000);

  // Cleanup quando la pagina viene chiusa
  window.addEventListener("beforeunload", () => {
    sessionManager.stopPersistentSessionRefresh();
    sessionManager.stopBackgroundRefresh();
  });

  // Riavvia il refresh quando la pagina diventa visibile (per gestire app in background)
  document.addEventListener("visibilitychange", () => {
    if (!document.hidden) {
      console.log("🔄 App tornata in foreground, riavvio refresh sessioni");
      sessionManager.startPersistentSessionRefresh();
      sessionManager.startBackgroundRefresh();
    }
  });
}

// Funzione specifica per query degli amici con timeout e gestione errori ottimizzata
export const queryWithTimeout = async <T>(
  operation: () => Promise<T>,
  timeoutMs: number = 10000, // 10 secondi di default
  operationName: string = "query"
): Promise<T> => {
  return new Promise((resolve, reject) => {
    const timer = setTimeout(() => {
      console.error(`🚨 Timeout ${operationName} dopo ${timeoutMs}ms`);
      reject(new Error(`Timeout: ${operationName} took too long`));
    }, timeoutMs);

    operation()
      .then((result) => {
        clearTimeout(timer);
        console.log(`✅ ${operationName} completata in tempo`);
        resolve(result);
      })
      .catch((error) => {
        clearTimeout(timer);
        console.error(`❌ Errore ${operationName}:`, error);
        reject(error);
      });
  });
};
