/**
 * 🎯 PROFILE CACHE SERVICE
 * Gestisce la cache dei profili utente per ridurre le chiamate API
 */

import { CACHE_TTL } from "@/utils/cache/simpleCacheHelper";
import type { PlayerStats } from "../localStatsService";

export interface ProfileData {
  stats: PlayerStats;
  isOnline: boolean;
  userId?: string;
  username?: string;
  customName?: string;
  lastSyncAt?: string;
  createdAt?: string;
  lastActive?: string;
}

// Cache state
let profileCache: ProfileData | null = null;
let cacheTimestamp = 0;
let cacheUserId: string | null = null;
const CACHE_DURATION = CACHE_TTL.MEDIUM; // 5 minuti

/**
 * Invalida la cache quando le statistiche vengono aggiornate
 */
export const invalidateProfileCache = (): void => {
  profileCache = null;
  cacheTimestamp = 0;
  cacheUserId = null;
  console.log("🗑️ Cache profilo invalidata");
};

/**
 * Forza l'aggiornamento della cache con nuovi dati
 */
export const forceUpdateProfileCache = (
  newStats: PlayerStats,
  username?: string
): void => {
  if (profileCache && cacheUserId) {
    profileCache.stats = newStats;
    if (username) {
      profileCache.username = username;
    }
    cacheTimestamp = Date.now(); // Rinnova timestamp
    console.log(
      "🔄 Cache aggiornata:",
      username ? `username: ${username}` : "solo stats"
    );
  }
};

/**
 * Aggiorna solo l'username nella cache
 */
export const updateUsernameInCache = (newUsername: string): void => {
  if (profileCache && cacheUserId) {
    profileCache.username = newUsername;
    cacheTimestamp = Date.now(); // Rinnova timestamp
    console.log("👤 Username aggiornato in cache:", newUsername);
  }
};

/**
 * Controlla se la cache è valida
 */
export const isCacheValid = (userId: string | null): boolean => {
  const now = Date.now();
  return (
    profileCache !== null &&
    cacheTimestamp > 0 &&
    now - cacheTimestamp < CACHE_DURATION &&
    cacheUserId === userId
  );
};

/**
 * Ottiene i dati dalla cache se valida
 */
export const getCachedProfile = (userId: string): ProfileData | null => {
  if (isCacheValid(userId)) {
    console.log("✅ Uso cache profilo valida, nessuna chiamata API");
    return profileCache;
  }
  return null;
};

/**
 * Salva i dati nella cache
 */
export const setCachedProfile = (
  profileData: ProfileData,
  userId: string
): void => {
  profileCache = profileData;
  cacheTimestamp = Date.now();
  cacheUserId = userId;
  console.log("💾 Profilo salvato in cache:", profileData.username || "N/A");
};

/**
 * Ottiene informazioni sullo stato della cache (per debug)
 */
export const getCacheInfo = () => ({
  hasCache: !!profileCache,
  timestamp: cacheTimestamp,
  userId: cacheUserId,
  isValid: isCacheValid(cacheUserId),
  age: cacheTimestamp ? Math.round((Date.now() - cacheTimestamp) / 1000) : 0,
});

/**
 * Pulisce la cache alla chiusura dell'app
 */
export const setupCacheCleanup = (): void => {
  if (typeof window !== "undefined") {
    window.addEventListener("beforeunload", () => {
      invalidateProfileCache();
    });
  }
};
