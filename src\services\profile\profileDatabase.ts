/**
 * 🗄️ PROFILE DATABASE SERVICE
 * Gestisce le operazioni database per i profili utente
 */

import { supabase } from "@/integrations/supabase/client";
import type { PlayerStats } from "../localStatsService";

/**
 * Funzione helper per retry operazioni database
 */
export const retryDatabaseOperation = async <T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> => {
  let lastError: any;

  for (let attempt = 0; attempt < maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      console.warn(
        `⚠️ Tentativo DB ${attempt + 1}/${maxRetries} fallito:`,
        error
      );

      if (attempt < maxRetries - 1) {
        // Backoff exponential: 1s, 2s, 4s
        const delay = baseDelay * Math.pow(2, attempt);
        console.log(`⏳ Attesa ${delay}ms prima del prossimo tentativo...`);
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }
  }

  throw lastError;
};

/**
 * Carica le statistiche online dell'utente
 */
export const loadOnlineStats = async (userId: string) => {
  console.log("🔄 Caricamento profilo da Supabase");

  const { data: statsData } = await supabase
    .from("game_stats")
    .select(
      `
      *,
      profiles!inner(username, created_at, updated_at)
    `
    )
    .eq("user_id", userId)
    .single();

  return statsData;
};

/**
 * Salva le statistiche online
 */
export const saveOnlineStats = async (
  userId: string,
  stats: PlayerStats
): Promise<void> => {
  await retryDatabaseOperation(async () => {
    const { error } = await supabase.from("game_stats").upsert(
      {
        user_id: userId,
        games_played: stats.totalGames,
        games_won: stats.gamesWon,
        level: stats.level,
        xp: stats.xp,
        updated_at: new Date().toISOString(),
      },
      { onConflict: "user_id" }
    );

    if (error) {
      throw error;
    }
  });
};

/**
 * Salva le statistiche usando fetch diretto (per problemi di sessione)
 */
export const saveOnlineStatsWithFetch = async (
  userId: string,
  stats: PlayerStats,
  accessToken: string
): Promise<void> => {
  const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
  const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

  const upsertResponse = await fetch(
    `${supabaseUrl}/rest/v1/game_stats?on_conflict=user_id`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        apikey: supabaseKey,
        Authorization: `Bearer ${accessToken}`,
        Prefer: "resolution=merge-duplicates",
      },
      body: JSON.stringify({
        user_id: userId,
        games_played: stats.totalGames,
        games_won: stats.gamesWon,
        level: stats.level,
        xp: stats.xp,
        updated_at: new Date().toISOString(),
      }),
    }
  );

  if (!upsertResponse.ok) {
    throw new Error(
      `HTTP ${upsertResponse.status}: ${upsertResponse.statusText}`
    );
  }
};

/**
 * Sincronizza le statistiche al login
 */
export const syncStatsOnLogin = async (): Promise<{
  success: boolean;
  syncedStats?: PlayerStats;
  conflicts?: string[];
}> => {
  try {
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      return { success: false };
    }

    console.log("🔄 Iniziando sincronizzazione al login...");

    // 1. Leggi statistiche locali
    const { getPlayerStats: getLocalPlayerStats } = await import(
      "../localStatsService"
    );
    const localStats = getLocalPlayerStats();

    // 2. Leggi statistiche online
    const { data: onlineData, error } = await supabase
      .from("game_stats")
      .select("games_played, games_won, level, xp, updated_at")
      .eq("user_id", user.id)
      .single();

    if (error && error.code !== "PGRST116") {
      console.warn("Errore lettura stats online:", error);
      return { success: false };
    }

    // 3. Se non ci sono dati online, carica quelli locali
    if (!onlineData) {
      console.log("📤 Nessun dato online, carico stats locali");
      await saveOnlineStats(user.id, localStats);
      return { success: true, syncedStats: localStats };
    }

    // 4. Confronta e risolvi conflitti
    const conflicts: string[] = [];
    const syncedStats = { ...localStats };

    // Usa sempre il valore più alto per evitare perdite di dati
    if (onlineData.games_played > localStats.totalGames) {
      conflicts.push("games_played");
      syncedStats.totalGames = onlineData.games_played;
    }

    if (onlineData.games_won > localStats.gamesWon) {
      conflicts.push("games_won");
      syncedStats.gamesWon = onlineData.games_won;
    }

    if (onlineData.level > localStats.level) {
      conflicts.push("level");
      syncedStats.level = onlineData.level;
    }

    if (onlineData.xp > localStats.xp) {
      conflicts.push("xp");
      syncedStats.xp = onlineData.xp;
    }

    // 5. Salva stats sincronizzate
    const { savePlayerStats: updateLocalPlayerStats } = await import(
      "../localStatsService"
    );
    updateLocalPlayerStats(syncedStats);

    // 6. Aggiorna online
    await saveOnlineStats(user.id, syncedStats);

    if (conflicts.length > 0) {
      console.log("🔒 Conflitti risolti durante sincronizzazione:", conflicts);
    }

    return { success: true, syncedStats, conflicts };
  } catch (error) {
    console.error("❌ Errore sincronizzazione:", error);
    return { success: false };
  }
};
