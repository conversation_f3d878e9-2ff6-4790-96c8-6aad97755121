/**
 * 🎯 PROFILE SERVICE - REFACTORED
 * Servizio principale per la gestione dei profili utente
 * Ora utilizza moduli separati per cache, calcoli e database
 */

import {
  getPlayerStats as getLocalPlayerStats,
  savePlayerStats as updateLocalPlayerStats,
  type PlayerStats,
} from "./localStatsService";
import { supabase } from "@/integrations/supabase/client";
import { queueStatsUpdate } from "./statsQueue";

// Import dei moduli estratti
import {
  type ProfileData,
  invalidateProfileCache,
  forceUpdateProfileCache,
  updateUsernameInCache,
  getCachedProfile,
  setCachedProfile,
  setupCacheCleanup,
  getCacheInfo,
} from "./profile/profileCache";

import {
  type GameData,
  type StatsUpdateResult,
  calculateStatsUpdate,
  calculateAbandonmentUpdate,
} from "./profile/statsCalculator";

import {
  retryDatabaseOperation,
  loadOnlineStats,
  saveOnlineStats,
  saveOnlineStatsWithFetch,
  syncStatsOnLogin,
} from "./profile/profileDatabase";

import {
  getReliableCreatedAt,
  convertDbStatsToLocal,
  validateStats,
  createLocalFallbackProfile,
  createDebugFunctions,
} from "./profile/profileUtils";

// Re-export delle interfacce e funzioni principali per compatibilità
export { 
  type ProfileData, 
  invalidateProfileCache, 
  forceUpdateProfileCache, 
  updateUsernameInCache 
};

// Cache per auth (mantenuto per compatibilità)
let authCache: any = null;
let authCacheTimestamp = 0;

export const invalidateAuthCache = (): void => {
  authCache = null;
  authCacheTimestamp = 0;
};

/**
 * 🎯 FUNZIONE PRINCIPALE: Ottiene il profilo attivo (online o locale)
 */
export const getActiveProfile = async (): Promise<ProfileData> => {
  try {
    // Controlla prima se abbiamo cache valida
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      return getLocalProfile();
    }

    // Usa cache se valida
    const cachedProfile = getCachedProfile(user.id);
    if (cachedProfile) {
      return cachedProfile;
    }

    // Sincronizzazione al primo accesso
    const syncResult = await syncStatsOnLogin();
    if (syncResult.success && syncResult.conflicts) {
      console.log(
        "⚠️ Rilevati conflitti durante sincronizzazione:",
        syncResult.conflicts
      );
    }

    // Carica profilo da database
    try {
      console.log("🔄 Caricamento profilo da Supabase");
      const statsData = await loadOnlineStats(user.id);

      console.log("📊 Dati ricevuti:", {
        username: statsData?.profiles?.username,
        level: statsData?.level,
        games: statsData?.games_played,
        created_at: statsData?.profiles?.created_at,
        updated_at: statsData?.profiles?.updated_at,
      });

      if (!statsData) {
        console.log("📊 Nessun dato online trovato, uso profilo locale");
        return getLocalProfile();
      }

      // Converti e valida le statistiche
      const username = statsData.profiles?.username || `Giocatore${user.id.slice(-4)}`;
      const onlineStats = validateStats(convertDbStatsToLocal(statsData, username));

      // Gestione intelligente date per retrocompatibilità
      const localStats = getLocalPlayerStats();
      const createdAt = getReliableCreatedAt(
        statsData?.profiles?.created_at,
        statsData?.games_played || 0,
        localStats.createdAt
      );

      const profileData: ProfileData = {
        stats: onlineStats,
        isOnline: true,
        userId: user.id,
        username: username,
        lastSyncAt: new Date().toISOString(),
        createdAt: createdAt,
        lastActive: statsData?.profiles?.updated_at || new Date().toISOString(),
      };

      // Sincronizza con altri sistemi
      await syncToOtherSystems(onlineStats, username);

      // Salva in cache
      setCachedProfile(profileData, user.id);

      return profileData;
    } catch (error) {
      console.warn("Errore caricamento stats online, uso locale:", error);
      return getLocalProfile();
    }
  } catch (error) {
    console.error("Errore getActiveProfile:", error);
    return getLocalProfile();
  }
};

/**
 * Profilo locale di fallback
 */
export const getLocalProfile = (): ProfileData => {
  return createLocalFallbackProfile();
};

/**
 * 🎮 AGGIORNA STATISTICHE DOPO PARTITA
 */
export const updateStatsAfterGame = async (gameData: GameData): Promise<StatsUpdateResult> => {
  try {
    // Ottieni statistiche correnti
    const currentProfile = await getActiveProfile();
    const updatedResult = calculateStatsUpdate(currentProfile.stats, gameData);

    // Aggiorna localmente
    updateLocalPlayerStats(updatedResult.stats);

    // Aggiorna cache
    forceUpdateProfileCache(updatedResult.stats, currentProfile.username);

    // Prova aggiornamento online
    if (currentProfile.isOnline && currentProfile.userId) {
      await saveStatsOnline(currentProfile.userId, updatedResult.stats);
    }

    return updatedResult;
  } catch (error) {
    console.error("❌ Errore updateStatsAfterGame:", error);
    throw error;
  }
};

/**
 * 🚪 AGGIORNA STATISTICHE DOPO ABBANDONO
 */
export const updateStatsAfterAbandonment = async (abandonmentData: {
  difficulty: "easy" | "medium" | "hard";
  playerTeam: number;
  currentScore: [number, number];
}): Promise<StatsUpdateResult> => {
  try {
    const currentProfile = await getActiveProfile();
    const updatedResult = calculateAbandonmentUpdate(currentProfile.stats, abandonmentData);

    // Aggiorna localmente
    updateLocalPlayerStats(updatedResult.stats);

    // Aggiorna cache
    forceUpdateProfileCache(updatedResult.stats, currentProfile.username);

    // Prova aggiornamento online
    if (currentProfile.isOnline && currentProfile.userId) {
      await saveStatsOnline(currentProfile.userId, updatedResult.stats);
    }

    return updatedResult;
  } catch (error) {
    console.error("❌ Errore updateStatsAfterAbandonment:", error);
    throw error;
  }
};

/**
 * Salva le statistiche online con gestione errori
 */
const saveStatsOnline = async (userId: string, stats: PlayerStats): Promise<void> => {
  try {
    await saveOnlineStats(userId, stats);
    console.log("✅ Statistiche salvate online con successo");
  } catch (error) {
    console.warn("⚠️ Salvataggio online fallito:", error);

    // Aggiungi alla coda per retry automatico
    try {
      const queueId = queueStatsUpdate(userId, stats, "high");
      console.log(`📤 Statistiche aggiunte alla coda retry: ${queueId}`);

      // Forza sincronizzazione immediata
      setTimeout(async () => {
        try {
          const { forceSyncStats } = await import("./statsQueue");
          await Promise.race([
            forceSyncStats(),
            new Promise((_, reject) =>
              setTimeout(() => reject(new Error("Sync timeout")), 5000)
            ),
          ]);
          console.log("🔄 Sincronizzazione forzata completata");
        } catch (forceSyncError) {
          console.warn("⚠️ Sincronizzazione forzata fallita:", forceSyncError);
        }
      }, 1000);
    } catch (queueError) {
      console.warn("⚠️ Impossibile aggiungere alla coda:", queueError);
    }
  }
};

/**
 * Sincronizza con altri sistemi per compatibilità
 */
const syncToOtherSystems = async (stats: PlayerStats, username: string): Promise<void> => {
  try {
    const { saveStats } = await import("./statsManager");
    const { updateUserProfile, saveUnifiedData } = await import("./unifiedStorageService");

    // Sincronizza statistiche con il nuovo sistema
    saveStats({
      level: stats.level,
      xp: stats.xp,
      gamesPlayed: stats.totalGames,
      gamesWon: stats.gamesWon,
      winRate: stats.winRate,
      currentWinStreak: stats.currentWinStreak,
      bestWinStreak: stats.bestWinStreak,
      lastPlayed: stats.lastPlayed,
    });

    updateUserProfile({ username });
    saveUnifiedData({ isOfflineMode: false });

    console.log("🔄 Username e modalità online sincronizzati:", username);
  } catch (error) {
    console.warn("⚠️ Errore sincronizzazione con altri sistemi:", error);
  }
};

/**
 * 🔄 FUNZIONI DI UTILITÀ
 */
export const refreshOnlineProfile = async (): Promise<ProfileData> => {
  invalidateProfileCache();
  return await getActiveProfile();
};

export const onProfileUpdate = (callback: (profile: ProfileData) => void): void => {
  console.log("onProfileUpdate callback registrato");
};

// Setup cleanup e debug functions
setupCacheCleanup();

if (process.env.NODE_ENV === "development") {
  // @ts-expect-error - Debug functions for development
  window.debugStats = {
    ...createDebugFunctions(),
    getProfile: async () => {
      const profile = await getActiveProfile();
      console.log("📊 Profilo corrente:", profile);
      return profile;
    },
    clearCache: () => {
      invalidateProfileCache();
      console.log("🔄 Cache profilo pulita");
    },
    checkCache: () => {
      console.log("📊 Stato cache:", getCacheInfo());
    },
    syncStats: async () => {
      const result = await syncStatsOnLogin();
      console.log("🔄 Sincronizzazione manuale:", result);
      return result;
    },
  };

  console.log("🐛 Debug functions available at window.debugStats");
}
